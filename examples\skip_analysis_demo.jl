#!/usr/bin/env julia

"""
Skip值分析功能演示
展示如何將開獎號碼轉換為Skip值進行分析
"""

# 載入模組
include("../src/LotteryAnalysis.jl")
using .LotteryAnalysis

"""
演示Skip值分析功能
"""
function demo_skip_analysis()
    println("🔢 Skip值分析功能演示")
    println("="^50)
    
    # 載入數據
    println("📊 載入歷史數據...")
    historical_data = load_real_data()
    
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end
    
    total_periods = length(historical_data)
    println("✅ 成功載入 $total_periods 期數據")
    
    # 示例1：計算最新期的Skip值
    println("\n" * "="^60)
    println("示例1：計算最新期開獎號碼的Skip值")
    println("="^60)
    
    try
        latest_draw = historical_data[1]
        skip_values = calculate_skip_values_for_draw(1, historical_data)
        
        println("📅 最新期 ($(latest_draw.draw_date))：")
        println("開獎號碼：$(join([lpad(n, 2) for n in latest_draw.numbers], " "))")
        println("Skip值：  $(join([lpad(s, 2) for s in skip_values], " "))")
        
        # 分析每個號碼
        println("\n詳細分析：")
        for (number, skip) in zip(latest_draw.numbers, skip_values)
            if skip == 0
                println("  號碼 $(number)：Skip=$(skip) (連續開出)")
            elseif skip <= 2
                println("  號碼 $(number)：Skip=$(skip) (短期重現)")
            elseif skip <= 5
                println("  號碼 $(number)：Skip=$(skip) (中期重現)")
            else
                println("  號碼 $(number)：Skip=$(skip) (長期重現)")
            end
        end
        
        # 統計摘要
        total_skip = sum(skip_values)
        avg_skip = round(total_skip / length(skip_values), digits=2)
        println("\n統計摘要：")
        println("  總Skip值：$(total_skip)")
        println("  平均Skip：$(avg_skip)")
        println("  Skip範圍：$(minimum(skip_values)) - $(maximum(skip_values))")
        
    catch e
        println("❌ 計算失敗：$e")
    end
    
    # 示例2：比較最近幾期的Skip模式
    println("\n" * "="^60)
    println("示例2：比較最近5期的Skip模式")
    println("="^60)
    
    println("期數  日期        開獎號碼           Skip值        總計  平均")
    println("-"^65)
    
    for i in 1:min(5, total_periods-1)
        try
            draw = historical_data[i]
            skip_values = calculate_skip_values_for_draw(i, historical_data)
            
            numbers_str = join([lpad(n, 2) for n in draw.numbers], " ")
            skips_str = join([lpad(s, 2) for s in skip_values], " ")
            total_skip = sum(skip_values)
            avg_skip = round(total_skip / length(skip_values), digits=1)
            
            println("$(lpad(i, 3))   $(draw.draw_date)  $(numbers_str)  $(skips_str)   $(lpad(total_skip, 3))  $(avg_skip)")
            
        catch e
            println("$(lpad(i, 3))   計算失敗：$e")
        end
    end
    
    # 示例3：Skip值分佈分析
    println("\n" * "="^60)
    println("示例3：Skip值分佈分析 (最近30期)")
    println("="^60)
    
    try
        skip_results = analyze_skip_patterns(historical_data, 30)
        
        if !isempty(skip_results)
            stats = get_skip_statistics(skip_results)
            
            println("📊 Skip值統計 (基於30期數據)：")
            println("  範圍：$(stats["skip_min"]) - $(stats["skip_max"])")
            println("  平均：$(stats["skip_mean"])")
            println("  中位數：$(stats["skip_median"])")
            println("  標準差：$(stats["skip_std"])")
            
            println("\n🔥 最常見的Skip值：")
            for (i, (skip, count)) in enumerate(stats["most_common_skip"][1:min(5, length(stats["most_common_skip"]))])
                percentage = round(count / stats["total_skip_values"] * 100, digits=1)
                println("  第$(i)名：Skip=$skip (出現 $count 次，佔 $percentage%)")
            end
            
            println("\n📈 每期總Skip統計：")
            println("  範圍：$(stats["total_skip_min"]) - $(stats["total_skip_max"])")
            println("  平均：$(stats["total_skip_mean"])")
            
        else
            println("❌ Skip分析失敗")
        end
        
    catch e
        println("❌ 分析失敗：$e")
    end
    
    # 示例4：特殊Skip模式識別
    println("\n" * "="^60)
    println("示例4：特殊Skip模式識別")
    println("="^60)
    
    try
        # 分析最近20期，尋找特殊模式
        special_patterns = []
        
        for i in 1:min(20, total_periods-1)
            draw = historical_data[i]
            skip_values = calculate_skip_values_for_draw(i, historical_data)
            
            # 檢查特殊模式
            if all(s -> s == 0, skip_values)
                push!(special_patterns, (i, "全部連續", draw.draw_date, skip_values))
            elseif all(s -> s <= 1, skip_values)
                push!(special_patterns, (i, "全部短Skip", draw.draw_date, skip_values))
            elseif maximum(skip_values) - minimum(skip_values) >= 10
                push!(special_patterns, (i, "極大Skip範圍", draw.draw_date, skip_values))
            elseif sum(skip_values) >= 25
                push!(special_patterns, (i, "高總Skip", draw.draw_date, skip_values))
            elseif sum(skip_values) <= 5
                push!(special_patterns, (i, "低總Skip", draw.draw_date, skip_values))
            end
        end
        
        if !isempty(special_patterns)
            println("🎯 發現特殊Skip模式：")
            for (period, pattern_type, date, skips) in special_patterns
                skips_str = join([lpad(s, 2) for s in skips], " ")
                println("  第$(period)期 ($(date))：$(pattern_type) - Skip值：$(skips_str)")
            end
        else
            println("📊 最近20期未發現特殊Skip模式")
        end
        
    catch e
        println("❌ 模式識別失敗：$e")
    end
    
    println("\n✅ Skip值分析演示完成！")
    println("\n💡 Skip值分析的應用價值：")
    println("  1. 🔍 趨勢識別：低Skip值表示號碼活躍，高Skip值表示號碼冷門")
    println("  2. 📊 模式分析：總Skip值反映整體開獎的活躍度")
    println("  3. 🎯 預測輔助：Skip值分佈可以輔助號碼選擇策略")
    println("  4. 📈 週期研究：Skip值變化可能反映號碼的週期性規律")
    println("  5. 🔄 比較分析：不同期數的Skip模式比較可發現趨勢變化")
end

# 運行演示
if abspath(PROGRAM_FILE) == @__FILE__
    demo_skip_analysis()
end
