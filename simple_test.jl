#!/usr/bin/env julia

# 簡單測試 Skip 深度分析功能

# 載入模組
include("src/LotteryAnalysis.jl")
include("src/SkipPatternAnalysis.jl")

using .LotteryAnalysis
using .SkipPatternAnalysis

println("🧪 簡單功能測試")

# 載入數據
historical_data = load_real_data()
println("✅ 載入 $(length(historical_data)) 期數據")

# 測試基本 Skip 分析
skip_results = analyze_skip_patterns(historical_data[1:50], nothing)
println("✅ 基本 Skip 分析正常，分析了 $(length(skip_results)) 期")

# 測試模組函數是否可用
println("測試模組函數...")
try
    # 測試函數是否存在
    @eval SkipPatternAnalysis.discover_hidden_patterns
    println("✅ discover_hidden_patterns 函數存在")
catch e
    println("❌ discover_hidden_patterns 函數不存在：$e")
end

try
    @eval SkipPatternAnalysis.predict_number_trends
    println("✅ predict_number_trends 函數存在")
catch e
    println("❌ predict_number_trends 函數不存在：$e")
end

try
    @eval SkipPatternAnalysis.optimize_selection_strategy
    println("✅ optimize_selection_strategy 函數存在")
catch e
    println("❌ optimize_selection_strategy 函數不存在：$e")
end

try
    @eval SkipPatternAnalysis.cross_validate_analysis
    println("✅ cross_validate_analysis 函數存在")
catch e
    println("❌ cross_validate_analysis 函數不存在：$e")
end

println("🎉 簡單測試完成")
