# 演示文件目錄

本目錄包含了AmiBroker圖表模擬器與Wonder Grid彩票分析系統的所有演示文件，展示各種功能的使用方法和實際效果。

## 📁 演示文件列表

### 1. 期數範圍分析演示
**文件：** `range_analysis_demo.jl`  
**功能：** 演示如何使用期數範圍分析功能  
**內容：**
- 分析最近N期數據的號碼表現
- 比較不同期數範圍的分析結果
- 對比近期與全部數據的有利號碼排名
- 展示 `get_recent_data()` 函數的使用

**運行方式：**
```bash
julia --project=. examples/range_analysis_demo.jl
```

**演示重點：**
- ✅ 靈活的時間範圍選擇
- ✅ 多期數比較分析
- ✅ 趨勢變化識別
- ✅ 數據範圍驗證

### 2. Skip值分析演示
**文件：** `skip_analysis_demo.jl`  
**功能：** 展示Skip值分析的基礎功能  
**內容：**
- 計算開獎號碼的Skip值
- 比較多期Skip模式
- Skip值分佈統計分析
- 特殊Skip模式識別

**運行方式：**
```bash
julia --project=. examples/skip_analysis_demo.jl
```

**演示重點：**
- ✅ Skip值計算原理
- ✅ 模式識別方法
- ✅ 統計分析結果
- ✅ 實際應用價值

### 3. Skip深度分析演示
**文件：** `skip_deep_analysis_demo.jl`  
**功能：** 綜合展示Skip分析的四大核心應用  
**內容：**
- 🔍 發現隱藏規律
- 🔮 提供預測線索
- 🎯 優化選號策略
- 🔬 驗證分析結果

**運行方式：**
```bash
julia --project=. examples/skip_deep_analysis_demo.jl
```

**演示重點：**
- ✅ 週期性模式發現
- ✅ 趨勢預測分析
- ✅ 策略優化比較
- ✅ 交叉驗證評估

## 🎯 演示文件使用指南

### 運行前準備
1. **確保數據文件存在：**
   ```
   data/fan5.csv  # 彩票歷史數據
   ```

2. **檢查依賴包：**
   ```bash
   julia --project=. -e "using Pkg; Pkg.instantiate()"
   ```

3. **驗證系統狀態：**
   ```bash
   julia --project=. src/Main.jl
   # 選擇 "8. 系統自檢"
   ```

### 演示順序建議
對於新用戶，建議按以下順序運行演示：

1. **基礎功能演示：**
   ```bash
   julia --project=. examples/range_analysis_demo.jl
   ```

2. **Skip分析入門：**
   ```bash
   julia --project=. examples/skip_analysis_demo.jl
   ```

3. **高級功能演示：**
   ```bash
   julia --project=. examples/skip_deep_analysis_demo.jl
   ```

### 演示內容說明

#### 📊 期數範圍分析演示
- **目標用戶：** 所有用戶
- **學習重點：** 理解時間範圍對分析結果的影響
- **實用價值：** 學會選擇合適的分析期數
- **預期時間：** 5-10分鐘

#### 🔢 Skip值分析演示
- **目標用戶：** 對Skip概念感興趣的用戶
- **學習重點：** 理解Skip值的計算和意義
- **實用價值：** 掌握Skip分析的基本方法
- **預期時間：** 10-15分鐘

#### 🧠 Skip深度分析演示
- **目標用戶：** 高級用戶和研究者
- **學習重點：** 掌握Skip分析的四大應用
- **實用價值：** 學會完整的Skip分析流程
- **預期時間：** 20-30分鐘

## 📈 演示結果解讀

### 期數範圍分析結果
- **短期分析（30-100期）：** 捕捉近期趨勢
- **中期分析（100-300期）：** 平衡穩定性
- **長期分析（全部數據）：** 最可靠統計

### Skip值分析結果
- **Skip=0：** 連續開出
- **Skip=1-2：** 短期重現
- **Skip=3-7：** 中期重現
- **Skip>7：** 長期重現

### Skip深度分析結果
- **隱藏規律：** 週期性、連續性、極值模式
- **預測線索：** 趨勢方向、異常檢測、週期預測
- **策略優化：** 激進、保守、平衡三種策略
- **驗證結果：** 準確率、命中率、改進幅度

## 🔧 故障排除

### 常見問題

1. **演示無法運行**
   ```bash
   # 檢查文件路徑
   ls examples/
   
   # 檢查Julia環境
   julia --project=. -e "println(\"環境正常\")"
   ```

2. **數據載入失敗**
   ```bash
   # 檢查數據文件
   ls data/fan5.csv
   
   # 檢查數據格式
   head -5 data/fan5.csv
   ```

3. **模組載入錯誤**
   ```bash
   # 檢查模組文件
   ls src/*.jl
   
   # 測試模組載入
   julia --project=. -e "include(\"src/LotteryAnalysis.jl\")"
   ```

### 性能優化

1. **大數據量處理：**
   - 減少分析期數（建議<300期）
   - 使用範圍分析功能
   - 關閉不必要的輸出

2. **記憶體優化：**
   - 定期重啟Julia會話
   - 避免同時運行多個演示
   - 清理不需要的變數

## 📝 自定義演示

### 創建新演示
1. **複製現有演示文件**
2. **修改分析參數**
3. **添加自定義功能**
4. **更新文檔說明**

### 演示模板
```julia
#!/usr/bin/env julia

"""
自定義演示模板
"""

include("../src/LotteryAnalysis.jl")
using .LotteryAnalysis

function custom_demo()
    println("🎯 自定義演示")
    
    # 載入數據
    historical_data = load_real_data()
    
    # 自定義分析
    # ... 你的代碼 ...
    
    println("✅ 演示完成！")
end

if abspath(PROGRAM_FILE) == @__FILE__
    custom_demo()
end
```

## 📞 技術支持

如果在運行演示過程中遇到問題：

1. **檢查系統要求**
2. **查看故障排除指南**
3. **運行系統自檢**
4. **查看詳細錯誤信息**

## 🎉 總結

這些演示文件展示了系統的完整功能，從基礎的期數範圍分析到高級的Skip深度分析，為用戶提供了全面的學習和實踐機會。通過運行這些演示，您可以：

- 🔍 理解各種分析方法的原理
- 📊 掌握實際的操作技巧
- 🎯 學會解讀分析結果
- 💡 獲得實用的應用建議

建議按順序運行所有演示，以獲得最佳的學習效果！
