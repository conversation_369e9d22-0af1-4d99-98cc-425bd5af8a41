#!/usr/bin/env julia

"""
Skip深度分析功能測試腳本
測試所有新增的深度分析功能
"""

# 載入模組
include("src/LotteryAnalysis.jl")
include("src/SkipPatternAnalysis.jl")

using .LotteryAnalysis
using .SkipPatternAnalysis

function test_skip_deep_analysis()
    println("🧪 Skip深度分析功能測試")
    println("="^50)
    
    # 載入測試數據
    println("📊 載入測試數據...")
    historical_data = load_real_data()
    
    if isempty(historical_data)
        println("❌ 無法載入數據，測試終止")
        return
    end
    
    println("✅ 成功載入 $(length(historical_data)) 期數據")
    
    # 測試1：隱藏規律發現
    println("\n🔍 測試1：隱藏規律發現")
    println("-"^30)
    try
        patterns = discover_hidden_patterns(historical_data, 100)
        println("✅ 隱藏規律發現功能正常")
        println("   發現 $(length(patterns)) 個模式")

        if !isempty(patterns)
            println("   示例模式：$(patterns[1].pattern_type)")
        end
    catch e
        println("❌ 隱藏規律發現測試失敗：$e")
    end
    
    # 測試2：號碼趨勢預測
    println("\n🔮 測試2：號碼趨勢預測")
    println("-"^30)
    try
        trends = predict_number_trends(historical_data, 50)
        println("✅ 號碼趨勢預測功能正常")
        println("   分析了 $(length(trends)) 個號碼")

        if !isempty(trends)
            high_confidence = [t for t in trends if t.confidence_level > 0.6]
            println("   高信心度預測：$(length(high_confidence)) 個")
        end
    catch e
        println("❌ 號碼趨勢預測測試失敗：$e")
    end
    
    # 測試3：選號策略優化
    println("\n🎯 測試3：選號策略優化")
    println("-"^30)
    for strategy_type in ["aggressive", "conservative", "balanced"]
        try
            strategy = optimize_selection_strategy(historical_data, strategy_type)
            println("✅ $(strategy_type)策略優化正常")
            println("   推薦號碼數：$(length(strategy["primary_numbers"]))")
        catch e
            println("❌ $(strategy_type)策略優化測試失敗：$e")
        end
    end
    
    # 測試4：交叉驗證分析
    println("\n🔬 測試4：交叉驗證分析")
    println("-"^30)
    try
        validation_results = cross_validate_analysis(historical_data, 100)
        println("✅ 交叉驗證分析功能正常")

        if haskey(validation_results, "skip_prediction_accuracy")
            acc = validation_results["skip_prediction_accuracy"]
            println("   Skip預測準確率：$(acc["accuracy_rate"])%")
        end

        if haskey(validation_results, "strategy_performance")
            println("   策略效果驗證：完成")
        end
    catch e
        println("❌ 交叉驗證分析測試失敗：$e")
    end
    
    println("\n🎉 Skip深度分析功能測試完成！")
end

# 執行測試
if abspath(PROGRAM_FILE) == @__FILE__
    test_skip_deep_analysis()
end
