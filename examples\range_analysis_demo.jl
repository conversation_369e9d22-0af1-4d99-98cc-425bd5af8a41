#!/usr/bin/env julia

"""
期數範圍分析示例
演示如何使用新的期數範圍分析功能
"""

# 載入模組
include("../src/LotteryAnalysis.jl")
using .LotteryAnalysis

"""
演示期數範圍分析功能
"""
function demo_range_analysis()
    println("🎯 期數範圍分析功能演示")
    println("="^50)
    
    # 載入數據
    println("📊 載入歷史數據...")
    historical_data = load_real_data()
    
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end
    
    total_periods = length(historical_data)
    println("✅ 成功載入 $total_periods 期數據")
    println("📅 數據範圍：$(historical_data[end].draw_date) 到 $(historical_data[1].draw_date)")
    
    # 示例1：分析最近50期的號碼13
    println("\n" * "="^60)
    println("示例1：分析號碼13在最近50期的表現")
    println("="^60)
    
    try
        stats_50 = analyze_number_statistics_range(13, historical_data, 1, 50)
        
        println("📊 最近50期分析結果：")
        range_info = stats_50["analysis_range"]
        println("  分析期數：$(range_info["actual_periods"])")
        println("  日期範圍：$(range_info["start_date"]) 到 $(range_info["end_date"])")
        println("  出現次數：$(stats_50["appearances"]) / $(stats_50["total_draws"])")
        println("  出現頻率：$(round(stats_50["frequency"], digits=4))")
        println("  當前Skip：$(stats_50["current_skip"])")
        println("  實際FFG：$(stats_50["empirical_ffg"])")
        println("  時機評分：$(stats_50["timing_score"])/100")
        println("  是否有利：$(stats_50["is_favorable"] ? "是" : "否")")
        
    catch e
        println("❌ 分析失敗：$e")
    end
    
    # 示例2：比較不同期數範圍的分析結果
    println("\n" * "="^60)
    println("示例2：比較號碼32在不同期數範圍的表現")
    println("="^60)
    
    periods_to_test = [30, 100, 300]
    
    println("期數範圍    出現頻率    當前Skip    時機評分    是否有利")
    println("-"^55)
    
    for periods in periods_to_test
        try
            if periods <= total_periods
                stats = analyze_number_statistics_range(32, historical_data, 1, periods)
                favorable = stats["is_favorable"] ? "是" : "否"
                println("$(lpad(periods, 6))      $(lpad(round(stats["frequency"], digits=4), 8))      $(lpad(stats["current_skip"], 6))      $(lpad(stats["timing_score"], 6))      $favorable")
            end
        catch e
            println("$(lpad(periods, 6))      分析失敗：$e")
        end
    end
    
    # 全部數據比較
    try
        stats_all = analyze_number_statistics(32, historical_data)
        favorable = stats_all["is_favorable"] ? "是" : "否"
        println("$(lpad("全部", 6))      $(lpad(round(stats_all["frequency"], digits=4), 8))      $(lpad(stats_all["current_skip"], 6))      $(lpad(stats_all["timing_score"], 6))      $favorable")
    catch e
        println("$(lpad("全部", 6))      分析失敗：$e")
    end
    
    # 示例3：比較最近100期與全部數據的有利號碼
    println("\n" * "="^60)
    println("示例3：比較最近100期與全部數據的有利號碼排名")
    println("="^60)
    
    try
        # 最近100期
        recent_favorable = find_favorable_numbers_range(historical_data, 1, 100, 10)
        recent_top_10 = [num for (num, _) in recent_favorable]
        
        # 全部數據
        all_favorable = find_favorable_numbers(historical_data, 10)
        all_top_10 = [num for (num, _) in all_favorable]
        
        println("最近100期前10名：$(join(lpad.(recent_top_10, 2), " "))")
        println("全部數據前10名：$(join(lpad.(all_top_10, 2), " "))")
        
        # 分析差異
        common = intersect(recent_top_10, all_top_10)
        recent_only = setdiff(recent_top_10, all_top_10)
        all_only = setdiff(all_top_10, recent_top_10)
        
        println("\n📊 比較分析：")
        println("  共同號碼：$(join(lpad.(common, 2), " ")) ($(length(common))/10)")
        if !isempty(recent_only)
            println("  近期新興：$(join(lpad.(recent_only, 2), " "))")
        end
        if !isempty(all_only)
            println("  長期穩定：$(join(lpad.(all_only, 2), " "))")
        end
        
    catch e
        println("❌ 比較分析失敗：$e")
    end
    
    # 示例4：展示get_recent_data函數的使用
    println("\n" * "="^60)
    println("示例4：get_recent_data函數使用示例")
    println("="^60)
    
    try
        # 獲取最近10期數據
        recent_10 = get_recent_data(historical_data, 1, 10)
        println("最近10期數據：")
        for (i, draw) in enumerate(recent_10)
            println("  第$(i)期 ($(draw.draw_date))：$(join(lpad.(draw.numbers, 2), " "))")
        end
        
        # 獲取從第5期開始的20期數據
        range_data = get_recent_data(historical_data, 5, 20)
        println("\n從第5期開始的20期數據範圍：")
        println("  起始：$(range_data[end].draw_date) (第$(length(range_data))期)")
        println("  結束：$(range_data[1].draw_date) (第1期)")
        println("  實際期數：$(length(range_data))")
        
    catch e
        println("❌ 數據獲取失敗：$e")
    end
    
    println("\n✅ 演示完成！")
    println("\n💡 使用建議：")
    println("  - 短期分析（30-100期）：適合捕捉近期趨勢")
    println("  - 中期分析（100-300期）：平衡趨勢與穩定性")
    println("  - 長期分析（全部數據）：獲得最穩定的統計結果")
    println("  - 比較分析：結合多個時間範圍獲得更全面的洞察")
end

# 運行演示
if abspath(PROGRAM_FILE) == @__FILE__
    demo_range_analysis()
end
