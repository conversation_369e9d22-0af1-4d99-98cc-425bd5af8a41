#!/usr/bin/env julia

"""
運行所有演示文件的腳本
按順序執行所有演示，展示系統的完整功能
"""

using Dates

"""
運行所有演示文件
"""
function run_all_demos()
    println("🎯 AmiBroker圖表模擬器與Wonder Grid分析系統")
    println("🎪 完整功能演示")
    println("="^60)
    
    start_time = now()
    
    # 演示列表
    demos = [
        ("期數範圍分析演示", "range_analysis_demo.jl", "展示靈活的時間範圍分析功能"),
        ("Skip值分析演示", "skip_analysis_demo.jl", "介紹Skip值的基本概念和應用"),
        ("Skip深度分析演示", "skip_deep_analysis_demo.jl", "完整展示Skip分析的四大應用")
    ]
    
    println("📋 演示計劃：")
    for (i, (name, file, desc)) in enumerate(demos)
        println("  $(i). $(name)")
        println("     文件：$(file)")
        println("     描述：$(desc)")
        println()
    end
    
    print("是否要運行所有演示？(y/N): ")
    response = strip(readline())
    
    if lowercase(response) != "y"
        println("👋 演示已取消")
        return
    end
    
    println("\n🚀 開始運行演示...")
    
    # 逐個運行演示
    for (i, (name, file, desc)) in enumerate(demos)
        println("\n" * "="^80)
        println("演示 $(i)/$(length(demos)): $(name)")
        println("="^80)
        println("📝 描述：$(desc)")
        println("📁 文件：examples/$(file)")
        println("⏰ 開始時間：$(now())")
        
        print("\n按 Enter 繼續，或輸入 's' 跳過此演示: ")
        user_input = strip(readline())
        
        if lowercase(user_input) == "s"
            println("⏭️  跳過演示：$(name)")
            continue
        end
        
        try
            println("\n🔄 正在運行演示...")
            
            # 運行演示文件
            demo_path = joinpath(@__DIR__, file)
            if isfile(demo_path)
                include(demo_path)
                println("\n✅ 演示完成：$(name)")
            else
                println("\n❌ 演示文件不存在：$(demo_path)")
            end
            
        catch e
            println("\n❌ 演示運行失敗：$(name)")
            println("錯誤信息：$e")
        end
        
        if i < length(demos)
            println("\n⏸️  演示暫停，按 Enter 繼續下一個演示...")
            readline()
        end
    end
    
    # 總結
    end_time = now()
    duration = end_time - start_time
    
    println("\n" * "="^80)
    println("🎉 所有演示完成！")
    println("="^80)
    println("⏰ 總耗時：$(duration)")
    println("📊 演示數量：$(length(demos))")
    
    println("\n💡 下一步建議：")
    println("  1. 🎯 運行主程式體驗完整功能：")
    println("     julia --project=. src/Main.jl")
    
    println("  2. 📚 閱讀詳細文檔：")
    println("     - examples/README.md")
    println("     - doc/Skip深度分析應用指南.md")
    
    println("  3. 🔬 進行系統自檢：")
    println("     julia --project=. src/Main.jl")
    println("     # 選擇 '8. 系統自檢'")
    
    println("  4. 🎲 開始實際分析：")
    println("     根據個人需求選擇合適的分析功能")
    
    println("\n🎯 感謝使用AmiBroker圖表模擬器與Wonder Grid分析系統！")
end

"""
顯示演示菜單
"""
function show_demo_menu()
    println("🎪 演示選單")
    println("-"^20)
    
    demos = [
        ("期數範圍分析演示", "range_analysis_demo.jl"),
        ("Skip值分析演示", "skip_analysis_demo.jl"),
        ("Skip深度分析演示", "skip_deep_analysis_demo.jl"),
        ("運行所有演示", "run_all_demos")
    ]
    
    while true
        println("\n請選擇要運行的演示：")
        for (i, (name, _)) in enumerate(demos)
            println("  $(i). $(name)")
        end
        println("  $(length(demos)+1). 退出")
        
        print("\n請選擇 (0-$(length(demos)+1)): ")
        choice = strip(readline())
        
        try
            choice_num = parse(Int, choice)
            
            if choice_num == length(demos)+1
                println("👋 再見！")
                break
            elseif choice_num == length(demos)
                # 運行所有演示
                run_all_demos()
                break
            elseif 1 <= choice_num <= length(demos)-1
                # 運行單個演示
                name, file = demos[choice_num]
                println("\n🚀 運行演示：$(name)")
                
                demo_path = joinpath(@__DIR__, file)
                if isfile(demo_path)
                    try
                        include(demo_path)
                        println("\n✅ 演示完成：$(name)")
                    catch e
                        println("\n❌ 演示運行失敗：$e")
                    end
                else
                    println("\n❌ 演示文件不存在：$(demo_path)")
                end
                
                print("\n按 Enter 返回選單...")
                readline()
            else
                println("❌ 無效選擇，請輸入 0-$(length(demos)+1)")
            end
            
        catch
            println("❌ 無效輸入，請輸入數字")
        end
    end
end

"""
主函數
"""
function main()
    println("🎯 AmiBroker圖表模擬器與Wonder Grid分析系統")
    println("🎪 演示運行器")
    println("="^50)
    
    println("選擇運行模式：")
    println("  1. 交互式選單")
    println("  2. 運行所有演示")
    print("\n請選擇 (1-2): ")
    
    mode = strip(readline())
    
    if mode == "1"
        show_demo_menu()
    elseif mode == "2"
        run_all_demos()
    else
        println("❌ 無效選擇，使用交互式選單")
        show_demo_menu()
    end
end

# 運行主函數
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
