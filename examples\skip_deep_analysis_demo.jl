#!/usr/bin/env julia

"""
Skip深度分析綜合演示
展示如何實現Skip分析的四大應用：
1. 發現隱藏規律
2. 提供預測線索  
3. 優化選號策略
4. 驗證分析結果
"""

# 載入模組
include("../src/LotteryAnalysis.jl")
include("../src/SkipPatternAnalysis.jl")

using .LotteryAnalysis
using .SkipPatternAnalysis

"""
綜合演示Skip深度分析的四大應用
"""
function comprehensive_skip_analysis_demo()
    println("🧠 Skip深度分析綜合演示")
    println("="^60)
    
    # 載入數據
    println("📊 載入歷史數據...")
    historical_data = load_real_data()
    
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end
    
    total_periods = length(historical_data)
    println("✅ 成功載入 $total_periods 期數據")
    
    # 1. 發現隱藏規律
    println("\n" * "="^80)
    println("1. 🔍 發現隱藏規律：傳統頻率分析可能忽略的時間規律")
    println("="^80)
    
    demo_hidden_patterns(historical_data)
    
    # 2. 提供預測線索
    println("\n" * "="^80)
    println("2. 🔮 提供預測線索：Skip值的變化可能預示號碼趨勢")
    println("="^80)
    
    demo_prediction_clues(historical_data)
    
    # 3. 優化選號策略
    println("\n" * "="^80)
    println("3. 🎯 優化選號策略：結合Skip分析制定更精準的投注策略")
    println("="^80)
    
    demo_strategy_optimization(historical_data)
    
    # 4. 驗證分析結果
    println("\n" * "="^80)
    println("4. 🔬 驗證分析結果：為其他分析方法提供交叉驗證")
    println("="^80)
    
    demo_cross_validation(historical_data)
    
    # 總結
    println("\n" * "="^80)
    println("📋 Skip深度分析總結")
    println("="^80)
    
    demo_summary()
end

"""
演示隱藏規律發現
"""
function demo_hidden_patterns(historical_data::Vector{LotteryDraw})
    println("🔍 正在分析最近150期數據，尋找隱藏規律...")
    
    try
        patterns = discover_hidden_patterns(historical_data, 150)
        
        if !isempty(patterns)
            println("\n📊 發現的隱藏規律：")
            
            for (i, pattern) in enumerate(patterns[1:min(3, length(patterns))])
                println("\n$(i). $(pattern.pattern_type)")
                println("   📝 描述：$(pattern.description)")
                println("   📈 出現頻率：$(pattern.frequency) 次")
                println("   🎯 信心度：$(round(pattern.confidence * 100, digits=1))%")
                println("   💡 預測價值：$(round(pattern.prediction_value, digits=1))")
                
                if !isempty(pattern.examples)
                    println("   📋 示例：")
                    for (period, date, skips) in pattern.examples[1:min(2, length(pattern.examples))]
                        skips_str = join([lpad(s, 2) for s in skips], " ")
                        println("     第$(period)期 ($(date))：Skip值 [$(skips_str)]")
                    end
                end
            end
            
            # 實際應用指導
            println("\n💡 如何觀察和應用這些規律：")
            
            high_confidence = [p for p in patterns if p.confidence > 0.6]
            if !isempty(high_confidence)
                println("  ✅ 高信心度規律 (>60%)：")
                for pattern in high_confidence[1:min(2, length(high_confidence))]
                    println("    • $(pattern.pattern_type)：可作為選號參考")
                    println("      觀察方法：$(get_observation_method(pattern.pattern_type))")
                end
            end
            
            medium_confidence = [p for p in patterns if 0.3 < p.confidence <= 0.6]
            if !isempty(medium_confidence)
                println("  ⚠️  中等信心度規律 (30-60%)：")
                for pattern in medium_confidence[1:min(2, length(medium_confidence))]
                    println("    • $(pattern.pattern_type)：需結合其他指標")
                end
            end
            
        else
            println("❌ 在當前數據中未發現明顯的隱藏規律")
            println("💡 建議：增加分析期數或調整分析參數")
        end
        
    catch e
        println("❌ 隱藏規律分析失敗：$e")
    end
end

"""
獲取觀察方法
"""
function get_observation_method(pattern_type::String)::String
    if pattern_type == "週期性模式"
        return "觀察總Skip值是否按週期變化，用於預測下期活躍度"
    elseif pattern_type == "連續低Skip模式"
        return "關注連續低Skip期數，判斷號碼活躍期的持續性"
    elseif pattern_type == "極值反彈模式"
        return "當出現極高Skip值時，關注下期是否出現反彈"
    elseif pattern_type == "總Skip-範圍相關性"
        return "通過總Skip值預測Skip範圍，評估號碼分散度"
    else
        return "持續觀察該模式的重複出現"
    end
end

"""
演示預測線索
"""
function demo_prediction_clues(historical_data::Vector{LotteryDraw})
    println("🔮 正在分析號碼趨勢，提供預測線索...")
    
    try
        trends = predict_number_trends(historical_data, 100)
        
        if !isempty(trends)
            # 分類展示預測線索
            println("\n📈 預測線索分析：")
            
            # 1. 高信心度預測
            high_confidence = [t for t in trends if t.confidence_level > 0.7]
            if !isempty(high_confidence)
                println("\n🎯 高信心度預測 (信心度>70%)：")
                for trend in high_confidence[1:min(5, length(high_confidence))]
                    direction_emoji = trend.trend_direction == "下降" ? "📉" : 
                                    trend.trend_direction == "上升" ? "📈" : "➡️"
                    println("  $(direction_emoji) 號碼 $(trend.number)：$(trend.trend_direction)趨勢")
                    println("     當前Skip=$(trend.current_skip)，預測Skip=$(round(trend.predicted_next_skip, digits=1))")
                    println("     信心度=$(round(trend.confidence_level * 100, digits=1))%")
                    
                    # 提供具體的預測線索
                    clue = generate_prediction_clue(trend)
                    println("     💡 預測線索：$(clue)")
                end
            end
            
            # 2. 異常檢測
            anomalies = [t for t in trends if t.anomaly_score > 0.8]
            if !isempty(anomalies)
                println("\n⚠️  異常號碼檢測 (可能的轉折信號)：")
                for trend in anomalies[1:min(3, length(anomalies))]
                    println("  🚨 號碼 $(trend.number)：異常分數=$(round(trend.anomaly_score, digits=2))")
                    println("     當前Skip=$(trend.current_skip)，歷史模式=$(trend.historical_pattern[1:min(5, length(trend.historical_pattern))])")
                    println("     💡 可能即將出現趨勢轉折")
                end
            end
            
            # 3. 週期性發現
            cyclic_numbers = [t for t in trends if t.cycle_length !== nothing]
            if !isempty(cyclic_numbers)
                println("\n🔄 週期性號碼 (發現規律週期)：")
                for trend in cyclic_numbers[1:min(3, length(cyclic_numbers))]
                    println("  🔁 號碼 $(trend.number)：週期長度=$(trend.cycle_length)")
                    println("     當前Skip=$(trend.current_skip)，預測下次出現在第$(round(trend.predicted_next_skip, digits=0))期後")
                end
            end
            
            println("\n💡 如何使用這些預測線索：")
            println("  1. 🎯 重點關注高信心度的下降趨勢號碼")
            println("  2. ⚠️  監控異常號碼的後續變化")
            println("  3. 🔄 利用週期性規律預測出現時機")
            println("  4. 📊 結合多個線索制定綜合策略")
            
        else
            println("❌ 趨勢預測分析失敗")
        end
        
    catch e
        println("❌ 預測線索分析失敗：$e")
    end
end

"""
生成預測線索
"""
function generate_prediction_clue(trend::SkipTrendAnalysis)::String
    if trend.trend_direction == "下降" && trend.current_skip > 5
        return "長期冷門號碼轉為活躍，建議重點關注"
    elseif trend.trend_direction == "下降" && trend.current_skip <= 2
        return "活躍號碼持續熱門，可考慮繼續跟進"
    elseif trend.trend_direction == "上升" && trend.current_skip <= 3
        return "活躍號碼可能轉冷，建議謹慎選擇"
    elseif trend.anomaly_score > 0.7
        return "Skip值異常，可能出現趨勢轉折"
    elseif trend.cycle_length !== nothing
        return "具有$(trend.cycle_length)期週期性，可預測出現時機"
    else
        return "趨勢$(trend.trend_direction)，建議持續觀察"
    end
end

"""
演示策略優化
"""
function demo_strategy_optimization(historical_data::Vector{LotteryDraw})
    println("🎯 正在優化選號策略...")
    
    try
        # 生成三種策略
        strategies = Dict{String,Any}()
        strategies["aggressive"] = optimize_selection_strategy(historical_data, "aggressive")
        strategies["conservative"] = optimize_selection_strategy(historical_data, "conservative")
        strategies["balanced"] = optimize_selection_strategy(historical_data, "balanced")
        
        println("\n📊 三種優化策略比較：")
        
        for (strategy_type, strategy) in strategies
            println("\n$(strategy["strategy_type"])：")
            println("  📝 描述：$(strategy["description"])")
            println("  ⚖️  風險等級：$(strategy["risk_level"])")
            println("  🎯 預期命中率：$(strategy["expected_hit_rate"])")
            println("  💡 建議：$(strategy["recommendation"])")
            
            if haskey(strategy, "primary_numbers")
                primary = strategy["primary_numbers"]
                println("  🔢 推薦號碼：$(join(lpad.(primary[1:min(10, length(primary))], 2), " "))")
            end
        end
        
        # 策略選擇指導
        println("\n💡 如何選擇和應用策略：")
        println("  🔥 激進策略：")
        println("     • 適用場景：短期投注，追求高回報")
        println("     • 觀察重點：異常高Skip號碼的反彈")
        println("     • 風險提醒：命中率波動較大")
        
        println("  🛡️  保守策略：")
        println("     • 適用場景：長期投注，追求穩定")
        println("     • 觀察重點：穩定趨勢號碼的持續性")
        println("     • 優勢：風險較低，適合新手")
        
        println("  ⚖️  平衡策略：")
        println("     • 適用場景：大多數投注者")
        println("     • 觀察重點：綜合評分的變化")
        println("     • 特點：風險與回報平衡")
        
        # 實際應用建議
        println("\n🎯 實際應用建議：")
        println("  1. 📊 定期更新分析（建議每10-20期）")
        println("  2. 🔄 根據驗證結果調整策略")
        println("  3. 📈 記錄策略表現，持續優化")
        println("  4. 🎲 不要完全依賴單一策略")
        
    catch e
        println("❌ 策略優化失敗：$e")
    end
end

"""
演示交叉驗證
"""
function demo_cross_validation(historical_data::Vector{LotteryDraw})
    println("🔬 正在進行交叉驗證分析...")
    
    if length(historical_data) < 100
        println("❌ 數據量不足，無法進行有效的交叉驗證")
        return
    end
    
    try
        validation_results = cross_validate_analysis(historical_data, 100)
        
        println("\n📊 交叉驗證結果：")
        
        # Skip預測準確性
        if haskey(validation_results, "skip_prediction_accuracy")
            skip_acc = validation_results["skip_prediction_accuracy"]
            println("\n🔮 Skip預測準確性驗證：")
            println("  準確率：$(skip_acc["accuracy_rate"])%")
            println("  平均誤差：$(skip_acc["average_error"])")
            
            # 解釋結果
            acc_rate = skip_acc["accuracy_rate"]
            if acc_rate > 60
                println("  ✅ 結論：Skip預測具有較高可信度")
            elseif acc_rate > 40
                println("  ⚠️  結論：Skip預測有一定參考價值")
            else
                println("  ❌ 結論：Skip預測準確性有限")
            end
        end
        
        # 策略效果驗證
        if haskey(validation_results, "strategy_performance")
            strategy_perf = validation_results["strategy_performance"]
            println("\n🎯 策略效果驗證：")
            
            best_strategy = ""
            best_rate = 0.0
            
            for (strategy_name, perf) in strategy_perf
                rate = parse(Float64, replace(perf["hit_rate"], "%" => ""))
                println("  $(strategy_name)：命中率 $(perf["hit_rate"])")
                
                if rate > best_rate
                    best_rate = rate
                    best_strategy = strategy_name
                end
            end
            
            if !isempty(best_strategy)
                println("  🏆 最佳策略：$(best_strategy) ($(best_rate)%)")
            end
        end
        
        # 與傳統方法比較
        if haskey(validation_results, "traditional_comparison")
            comparison = validation_results["traditional_comparison"]
            println("\n🔄 與傳統分析方法比較：")
            
            freq_rate = round(comparison["frequency_analysis"]["hit_rate"] * 100, digits=1)
            skip_rate = round(comparison["skip_analysis"]["hit_rate"] * 100, digits=1)
            improvement = comparison["improvement"]
            
            println("  📊 傳統頻率分析：$(freq_rate)%")
            println("  🧠 Skip值分析：$(skip_rate)%")
            println("  📈 改進幅度：$(improvement)%")
            
            if improvement > 5
                println("  ✅ Skip分析顯著優於傳統方法")
            elseif improvement > 0
                println("  ⚠️  Skip分析略優於傳統方法")
            else
                println("  ❌ Skip分析未顯示明顯優勢")
            end
        end
        
        println("\n💡 如何解讀和應用驗證結果：")
        println("  1. 📊 準確率>60%：可作為主要分析方法")
        println("  2. 📊 準確率40-60%：可作為輔助參考")
        println("  3. 📊 準確率<40%：需要改進分析方法")
        println("  4. 🔄 定期進行驗證，確保方法有效性")
        
    catch e
        println("❌ 交叉驗證失敗：$e")
    end
end

"""
總結演示
"""
function demo_summary()
    println("📋 Skip深度分析為彩票分析帶來的價值：")
    println()
    
    println("🔍 1. 發現隱藏規律：")
    println("   • 週期性模式：揭示總Skip值的週期變化")
    println("   • 連續性模式：識別號碼活躍期的持續性")
    println("   • 極值模式：發現極端Skip值後的反彈規律")
    println("   • 相關性模式：找出不同Skip指標間的關聯")
    
    println("\n🔮 2. 提供預測線索：")
    println("   • 趨勢預測：基於Skip變化預測號碼走勢")
    println("   • 異常檢測：識別可能的趨勢轉折信號")
    println("   • 週期預測：利用週期性預測出現時機")
    println("   • 信心評估：量化預測的可信度")
    
    println("\n🎯 3. 優化選號策略：")
    println("   • 激進策略：追求高回報，適合短期投注")
    println("   • 保守策略：追求穩定，適合長期投注")
    println("   • 平衡策略：風險回報平衡，適合大眾")
    println("   • 動態調整：根據驗證結果持續優化")
    
    println("\n🔬 4. 驗證分析結果：")
    println("   • 預測準確性：量化Skip預測的準確率")
    println("   • 策略效果：驗證不同策略的實際表現")
    println("   • 方法比較：與傳統分析方法對比")
    println("   • 持續改進：基於驗證結果優化方法")
    
    println("\n🎯 實際應用建議：")
    println("   1. 🔄 定期分析：每10-20期更新一次分析")
    println("   2. 📊 多維驗證：結合多種分析方法")
    println("   3. 📈 記錄追蹤：持續記錄分析效果")
    println("   4. 🎲 理性投注：不要過度依賴任何單一方法")
    
    println("\n✅ Skip深度分析演示完成！")
end

# 運行演示
if abspath(PROGRAM_FILE) == @__FILE__
    comprehensive_skip_analysis_demo()
end
