# AmiBroker圖表模擬器與Wonder Grid彩票分析系統

一個整合技術分析圖表功能與彩票統計分析的Julia專案。

## 功能特色

### 🎯 Wonder Grid 彩票分析
- **號碼統計分析**：計算每個號碼的出現頻率、Skip值、FFG中位數
- **有利號碼分析**：基於時機評分和冷熱評分找出最有利的號碼
- **配對頻率分析**：分析號碼間的配對關係
- **組合生成**：基於關鍵號碼生成Wonder Grid組合
- **🆕 期數範圍分析**：支援分析最近N期數據，靈活調整分析範圍
- **🆕 Skip值分析**：將開獎號碼轉換為Skip值進行深度分析

### 📊 圖表模擬器
- **文字版圖表**：ASCII藝術風格的價格走勢圖
- **專業圖表**：使用Plots.jl繪製互動式蠟燭圖
- **移動平均線**：支援多條移動平均線顯示
- **價格分佈分析**：直方圖分析價格分佈

## 快速開始

### 安裝依賴

```bash
# 激活專案環境
julia --project=.

# 安裝依賴包
julia> using Pkg
julia> Pkg.instantiate()
```

### 運行主程式

```bash
# 運行主選單（推薦新用戶）
julia --project=. src/Main.jl

# 或運行原有的完整系統
julia --project=. src/AmibrokerChartSimilator.jl
```

### 🎯 運行演示文件

```bash
# 期數範圍分析演示
julia --project=. examples/range_analysis_demo.jl

# Skip值分析演示
julia --project=. examples/skip_analysis_demo.jl

# Skip深度分析演示（完整功能展示）
julia --project=. examples/skip_deep_analysis_demo.jl
```

### 運行圖表模擬器

```bash
# 簡化增強版（推薦）
julia --project=. src/amibroker_chart_simple.jl fan5_01 weekly candlestick

# 文字版
julia --project=. src/amibroker_chart_simulator.jl fan5_01 weekly
```

## 專案結構

```
AmibrokerChartSimilator/
├── src/
│   ├── Main.jl                    # 主程式入口（新）
│   ├── LotteryAnalysis.jl         # 彩票分析模組（新）
│   ├── ChartUtils.jl              # 圖表工具模組（新）
│   ├── SkipPatternAnalysis.jl     # Skip深度分析模組（新）
│   ├── AmibrokerChartSimilator.jl # 原完整系統
│   ├── amibroker_chart_simple.jl  # 簡化圖表模擬器
│   ├── amibroker_chart_enhanced.jl # 增強圖表模擬器
│   └── amibroker_chart_simulator.jl # 基礎圖表模擬器
├── examples/                      # 演示文件目錄（新）
│   ├── README.md                  # 演示文件說明
│   ├── range_analysis_demo.jl     # 期數範圍分析演示
│   ├── skip_analysis_demo.jl      # Skip值分析演示
│   └── skip_deep_analysis_demo.jl # Skip深度分析演示
├── data/
│   └── fan5.csv                   # 彩票歷史數據
├── data_amibroker/
│   └── fan5/                      # 圖表數據
├── charts/                        # 生成的圖表文件
├── doc/                           # 文檔
│   └── Skip深度分析應用指南.md    # Skip分析詳細指南
└── Project.toml                   # 專案配置
```

## 數據格式

### 彩票數據 (data/fan5.csv)
```csv
YYYY-MM-DD,num1,num2,num3,num4,num5
2024-01-01,5,12,23,31,38
2024-01-04,2,15,22,29,35
```

### 圖表數據 (data_amibroker/fan5/g1/*.csv)
```csv
ticker,date,closes,volume
fan5_01,2024-01-01,25.5,1000
fan5_01,2024-01-02,26.0,1200
```

## 主要改進

### ✅ 模組化重構
- 將大型模組拆分為專門功能模組
- 提高代碼可維護性和重用性
- 清晰的模組界面和依賴關係

### ✅ 性能優化
- 使用並行計算加速號碼分析
- 預分配向量減少記憶體分配
- 優化排序算法

### ✅ 代碼簡化
- 創建統一的主程式入口
- 簡化用戶界面
- 減少代碼重複

### ✅ 錯誤處理
- 完善的輸入驗證
- 優雅的錯誤恢復機制
- 詳細的錯誤信息

### 🆕 期數範圍分析
- **靈活的時間範圍**：支援分析最近N期數據
- **比較分析**：對比不同時間範圍的分析結果
- **趨勢識別**：識別近期新興號碼和長期穩定號碼
- **範圍信息顯示**：清楚顯示分析的日期範圍和期數

### 🆕 Skip值分析
- **開獎轉Skip**：將每期開獎號碼轉換為對應的Skip值
- **模式識別**：識別特殊的Skip模式（連續開出、極大範圍等）
- **統計分析**：提供Skip值的分佈統計和趨勢分析
- **多維度觀察**：從Skip角度重新審視彩票開獎規律

## 使用建議

### 新用戶
建議使用新的主程式：
```bash
julia --project=. src/Main.jl
```

### 進階用戶
可以使用原有的完整系統：
```bash
julia --project=. src/AmibrokerChartSimilator.jl
```

### 圖表功能
推薦使用簡化增強版：
```bash
julia --project=. src/amibroker_chart_simple.jl
```

### 🆕 期數範圍分析使用指南

#### 1. 號碼統計分析（範圍版本）
```bash
# 在主選單選擇 "2. 號碼統計分析"
# 然後選擇 "2. 最近N期數據"
# 輸入期數，例如：100
# 輸入要分析的號碼，例如：32
```

#### 2. 有利號碼分析（範圍版本）
```bash
# 在主選單選擇 "3. 有利號碼分析"
# 然後選擇 "2. 最近N期數據"
# 輸入期數，例如：50
# 系統會自動比較與全部數據的差異
```

#### 3. 程式化使用
```julia
include("src/LotteryAnalysis.jl")
using .LotteryAnalysis

# 載入數據
data = load_real_data()

# 分析最近100期的號碼32
stats = analyze_number_statistics_range(32, data, 1, 100)

# 找出最近50期的有利號碼
favorable = find_favorable_numbers_range(data, 1, 50, 15)

# 獲取最近30期的數據
recent_data = get_recent_data(data, 1, 30)
```

#### 4. 🆕 Skip值分析使用
```bash
# 在主選單選擇 "5. Skip值分析"
# 然後選擇分析模式：
# 1. 單期Skip值查詢 - 查看特定期數的Skip值
# 2. Skip模式分析 - 分析多期的Skip模式
# 3. Skip統計摘要 - 獲得Skip值的統計分佈
```

#### 5. 程式化Skip分析
```julia
include("src/LotteryAnalysis.jl")
using .LotteryAnalysis

# 載入數據
data = load_real_data()

# 計算第1期的Skip值
skip_values = calculate_skip_values_for_draw(1, data)

# 分析最近30期的Skip模式
skip_results = analyze_skip_patterns(data, 30)

# 獲得Skip統計信息
stats = get_skip_statistics(skip_results)

# 顯示Skip分析結果
display_skip_analysis(skip_results, 20)
```

#### 6. 分析建議
- **短期分析（30-100期）**：適合捕捉近期趨勢和熱門號碼
- **中期分析（100-300期）**：平衡趨勢識別與統計穩定性
- **長期分析（全部數據）**：獲得最穩定可靠的統計結果
- **比較分析**：結合多個時間範圍獲得更全面的洞察
- **🆕 Skip分析**：從全新角度理解號碼開獎規律

## 系統要求

- **Julia**: 1.6+ (推薦 1.8+)
- **記憶體**: 最少 2GB RAM
- **儲存空間**: 100MB 用於數據和分析文件
- **作業系統**: Windows, macOS, Linux

## 可選依賴

- **Plots.jl**: 專業圖表功能（強烈推薦）
- **PlotlyJS.jl**: 互動式圖表後端
- **StatsPlots.jl**: 統計圖表功能

## 故障排除

### 常見問題

1. **找不到數據文件**
   - 確認 `data/fan5.csv` 文件存在
   - 檢查文件格式是否正確

2. **Plots.jl 載入失敗**
   ```bash
   julia --project=. -e "using Pkg; Pkg.add(\"Plots\")"
   ```

3. **記憶體不足**
   - 減少分析的數據量
   - 關閉其他程序釋放記憶體

### 系統自檢
```bash
julia --project=. src/Main.jl
# 選擇選項 6 進行系統自檢
```

## 📚 學習資源

### 演示文件
- **examples/README.md** - 演示文件完整說明
- **examples/range_analysis_demo.jl** - 期數範圍分析演示
- **examples/skip_analysis_demo.jl** - Skip值分析基礎演示
- **examples/skip_deep_analysis_demo.jl** - Skip深度分析完整演示

### 文檔資源
- **doc/Skip深度分析應用指南.md** - Skip分析詳細應用指南
- **README.md** - 專案總體說明文檔

### 學習路徑
1. **新手入門：** 運行主程式 → 系統自檢 → 基礎功能體驗
2. **進階學習：** 期數範圍分析演示 → Skip基礎演示
3. **專家級別：** Skip深度分析演示 → 自定義分析策略

## 開發計劃

- [x] ✅ 模組化重構
- [x] ✅ 期數範圍分析功能
- [x] ✅ Skip值分析功能
- [x] ✅ Skip深度分析四大應用
- [x] ✅ 交叉驗證機制
- [x] ✅ 完整演示文件
- [ ] 完善配對分析功能
- [ ] 添加更多技術指標
- [ ] 支援更多圖表類型
- [ ] 優化大數據處理性能
- [ ] 添加單元測試

## 授權

本專案採用 MIT 授權條款。

## 貢獻

歡迎提交 Issue 和 Pull Request！
